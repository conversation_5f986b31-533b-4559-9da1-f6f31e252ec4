<template>
	<div v-if="props.item?.check_lists?.[webcatalog3D]" class="cd-model-view">
		<!-- FIXME na klik se otvara 3d prikaz -->
		<BaseCmsLabel code="view_3d" tag="span" />
	</div>
</template>

<script setup>
	const config = useAppConfig();
	const props = defineProps(['item']);
	const origin = ['https://www.bigbang.si', 'https://beta.bigbang.si'].includes(config.host) ? true : false;
	const webcatalog3D = origin ? 'webcatalog_1219026' : 'webcatalog_2225499';
</script>

<style scoped lang="less">
	.cd-model-view{
		display: flex; align-items: center; justify-content: center; height: 40px; padding: 5px 15px; background: var(--white); border-radius: 100px; font-size: 16px; font-weight: 500; color: var(--gray5); position: absolute; right: 16px; bottom: 16px; box-shadow: 0px 0.764px 3.055px 0px rgba(0, 0, 0, 0.25); z-index: 1; cursor: pointer;
		span{
			padding-left: 34px; position: relative;
			&:before{.icon-d(); font: 24px/1 var(--fonti); color: var(--gray5); position: absolute; left: 0;}
		}

		@media (max-width: @m){
			width: 36px; height: 36px; margin-top: 8px; padding: 0; border-radius: 100%; position: relative; top: unset; right: unset; left: unset; bottom: unset; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);
			&:before{.icon-d(); font: 22px/1 var(--fonti); color: var(--gray5); position: absolute;}
			span{display: none;}
		}
	}
</style>